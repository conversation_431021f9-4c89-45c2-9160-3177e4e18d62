#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
file_manager.py

统一的文件命名管理系统，支持多种文档类型的处理
"""

import os
import re
import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging


class FileManager:
    """文件命名和路径管理器"""
    
    def __init__(self, base_dir: str = "documents"):
        """
        初始化文件管理器
        
        Args:
            base_dir: 基础目录，所有文档处理都在此目录下
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # 支持的文档类型
        self.supported_types = {
            '.pdf': 'pdf',
            '.docx': 'word',
            '.doc': 'word',
            '.txt': 'text',
            '.md': 'markdown'
        }
        
        # 创建标准子目录
        self.subdirs = {
            'source': self.base_dir / 'source',           # 原始文档
            'extracted': self.base_dir / 'extracted',     # 提取的内容
            'images': self.base_dir / 'images',           # 图片资源
            'anki': self.base_dir / 'anki',               # 生成的anki卡片
            'cache': self.base_dir / 'cache',             # 缓存文件
            'logs': self.base_dir / 'logs'                # 日志文件
        }
        
        # 创建所有子目录
        for subdir in self.subdirs.values():
            subdir.mkdir(exist_ok=True)
    
    def get_file_info(self, file_path: str) -> Dict[str, str]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            包含文件信息的字典
        """
        path = Path(file_path)
        ext = path.suffix.lower()
        
        info = {
            'original_path': str(path.absolute()),
            'filename': path.stem,
            'extension': ext,
            'type': self.supported_types.get(ext, 'unknown'),
            'size': path.stat().st_size if path.exists() else 0,
            'timestamp': datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        }
        
        # 生成标准化文件名（去除特殊字符）
        clean_name = re.sub(r'[^\w\-_\.]', '_', info['filename'])
        clean_name = re.sub(r'_+', '_', clean_name).strip('_')
        info['clean_name'] = clean_name
        
        return info
    
    def generate_project_name(self, file_info: Dict[str, str]) -> str:
        """
        为文档生成项目名称
        
        Args:
            file_info: 文件信息字典
            
        Returns:
            标准化的项目名称
        """
        return f"{file_info['clean_name']}_{file_info['timestamp']}"
    
    def get_extraction_paths(self, file_path: str) -> Dict[str, Path]:
        """
        获取文档提取相关的所有路径
        
        Args:
            file_path: 原始文件路径
            
        Returns:
            包含各种路径的字典
        """
        file_info = self.get_file_info(file_path)
        project_name = self.generate_project_name(file_info)
        
        # 为每个文档创建独立的提取目录
        extraction_dir = self.subdirs['extracted'] / project_name
        extraction_dir.mkdir(exist_ok=True)
        
        # 图片目录
        images_dir = extraction_dir / 'images'
        images_dir.mkdir(exist_ok=True)
        
        # 缓存目录
        cache_dir = self.subdirs['cache'] / project_name
        cache_dir.mkdir(exist_ok=True)
        
        paths = {
            'project_name': project_name,
            'extraction_dir': extraction_dir,
            'markdown_file': extraction_dir / f"{file_info['clean_name']}.md",
            'images_dir': images_dir,
            'cache_dir': cache_dir,
            'anki_file': self.subdirs['anki'] / f"{project_name}_flashcards.txt",
            'log_file': self.subdirs['logs'] / f"{project_name}.log"
        }
        
        return paths
    
    def get_image_name(self, project_name: str, page_num: int, img_index: int, 
                      source_type: str = 'page') -> str:
        """
        生成标准化的图片文件名
        
        Args:
            project_name: 项目名称
            page_num: 页码
            img_index: 图片索引
            source_type: 来源类型 (page, inline, etc.)
            
        Returns:
            标准化的图片文件名
        """
        return f"{project_name}_{source_type}_{page_num:03d}_img_{img_index:03d}.png"
    
    def get_chunk_cache_path(self, project_name: str, chunk_index: int) -> Path:
        """
        获取分块缓存文件路径
        
        Args:
            project_name: 项目名称
            chunk_index: 分块索引
            
        Returns:
            缓存文件路径
        """
        cache_dir = self.subdirs['cache'] / project_name
        return cache_dir / f"chunk_{chunk_index:04d}.json"
    
    def list_projects(self) -> List[Dict[str, str]]:
        """
        列出所有处理过的项目
        
        Returns:
            项目信息列表
        """
        projects = []
        
        for extraction_dir in self.subdirs['extracted'].iterdir():
            if extraction_dir.is_dir():
                # 查找markdown文件
                md_files = list(extraction_dir.glob('*.md'))
                if md_files:
                    md_file = md_files[0]
                    project_info = {
                        'name': extraction_dir.name,
                        'path': str(extraction_dir),
                        'markdown_file': str(md_file),
                        'has_images': (extraction_dir / 'images').exists(),
                        'image_count': len(list((extraction_dir / 'images').glob('*.png'))) if (extraction_dir / 'images').exists() else 0,
                        'has_cache': (self.subdirs['cache'] / extraction_dir.name).exists(),
                        'has_anki': (self.subdirs['anki'] / f"{extraction_dir.name}_flashcards.txt").exists()
                    }
                    projects.append(project_info)
        
        return sorted(projects, key=lambda x: x['name'])
    
    def cleanup_project(self, project_name: str, keep_anki: bool = True) -> bool:
        """
        清理项目文件
        
        Args:
            project_name: 项目名称
            keep_anki: 是否保留anki文件
            
        Returns:
            是否成功清理
        """
        try:
            # 删除提取目录
            extraction_dir = self.subdirs['extracted'] / project_name
            if extraction_dir.exists():
                import shutil
                shutil.rmtree(extraction_dir)
            
            # 删除缓存目录
            cache_dir = self.subdirs['cache'] / project_name
            if cache_dir.exists():
                import shutil
                shutil.rmtree(cache_dir)
            
            # 删除日志文件
            log_file = self.subdirs['logs'] / f"{project_name}.log"
            if log_file.exists():
                log_file.unlink()
            
            # 可选删除anki文件
            if not keep_anki:
                anki_file = self.subdirs['anki'] / f"{project_name}_flashcards.txt"
                if anki_file.exists():
                    anki_file.unlink()
            
            logging.info(f"项目 {project_name} 清理完成")
            return True
            
        except Exception as e:
            logging.error(f"清理项目 {project_name} 失败: {e}")
            return False
    
    def get_config_for_project(self, project_name: str) -> Dict[str, str]:
        """
        为项目生成配置信息
        
        Args:
            project_name: 项目名称
            
        Returns:
            项目配置字典
        """
        paths = self.get_extraction_paths(f"dummy_{project_name}")
        
        config = {
            'INPUT_FILE': str(paths['markdown_file']),
            'OUTPUT_FILE': str(paths['anki_file']),
            'CACHE_DIR': str(paths['cache_dir']),
            'PROJECT_NAME': project_name
        }
        
        return config


def main():
    """测试文件管理器功能"""
    # 创建文件管理器实例
    fm = FileManager()
    
    # 测试文件信息获取
    test_files = ['test.pdf', 'document.docx', 'notes.txt']
    
    for file_path in test_files:
        print(f"\n文件: {file_path}")
        file_info = fm.get_file_info(file_path)
        print(f"  信息: {file_info}")
        
        paths = fm.get_extraction_paths(file_path)
        print(f"  路径: {paths}")
    
    # 列出项目
    projects = fm.list_projects()
    print(f"\n现有项目: {len(projects)} 个")
    for project in projects:
        print(f"  - {project}")


if __name__ == "__main__":
    main()
