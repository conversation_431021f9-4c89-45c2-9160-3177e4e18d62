; Configuration for Multi-Document Processing and Anki flashcards generation
[DEFAULT]
; Document Processing Settings
BASE_DIR = documents
PDF_TOP_MARGIN = 50.0
PDF_BOTTOM_MARGIN = 50.0

; Legacy compatibility (will be overridden by document processor)
INPUT_PDF = None
INPUT_FILE = tiku_md/tiku.md
OUTPUT_FILE = ankiflashcards.txt
LINES_TO_SKIP = 13

; Processing Settings
CHUNK_SIZE = 100
CHUNK_STRIDE = 70
MAX_WORKERS = 10

; AI API Settings
API_BASE_URL = https://v2.voct.top/v1
API_PROVIDER = openai
; API_KEY can be set here or via environment variable  
API_KEY = fo-_to6t48Uxu76weXPT2rWE7U1mePzNdIF
MODEL_NAME = deepseek-ai/DeepSeek-V3
REQUEST_TIMEOUT = 120

; Cache Settings
CACHE_DIR = cache

; File Naming Settings
USE_TIMESTAMP = true
CLEAN_FILENAMES = true